# MySQL MCP 服务器配置
# 将此文件复制为 .env 并使用实际的数据库凭据更新

# MySQL数据库连接配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=your_password
MYSQL_DATABASE=your_database

# 连接池配置
MYSQL_CONNECTION_LIMIT=10
MYSQL_CONNECT_TIMEOUT=60000
MYSQL_IDLE_TIMEOUT=60000

# SSL配置（可选）
MYSQL_SSL=false
MYSQL_CHARSET=utf8mb4
MYSQL_TIMEZONE=local

# 安全配置
MAX_QUERY_LENGTH=10000
ALLOWED_QUERY_TYPES=SELECT,SHOW,DESCRIBE,INSERT,UPDATE,DELETE,CREATE,DROP,ALTER
MAX_RESULT_ROWS=1000
QUERY_TIMEOUT=30000

# 频率限制配置
RATE_LIMIT_WINDOW=60000
RATE_LIMIT_MAX=100

# 审计和安全配置
ENABLE_AUDIT_LOG=true
BLOCK_DANGEROUS_QUERIES=true

# 调试配置
DEBUG=false