/**
 * 性能监控工具
 * 用于监控MySQL MCP服务器的性能指标
 */
export interface PerformanceMetrics {
    queryCount: number;
    averageQueryTime: number;
    slowQueryCount: number;
    errorCount: number;
    connectionCount: number;
    memoryUsage: {
        rss: number;
        heapTotal: number;
        heapUsed: number;
        external: number;
        arrayBuffers: number;
    };
    uptime: number;
}
export declare class PerformanceMonitor {
    private metrics;
    private startTime;
    private queryTimes;
    private slowQueryThreshold;
    constructor(slowQueryThreshold?: number);
    /**
     * 记录查询执行
     * @param executionTime 执行时间（毫秒）
     * @param success 是否成功
     */
    recordQuery(executionTime: number, success?: boolean): void;
    /**
     * 记录连接获取
     */
    recordConnection(): void;
    /**
     * 更新平均查询时间
     */
    private updateAverageQueryTime;
    /**
     * 获取当前性能指标
     * @returns PerformanceMetrics 性能指标
     */
    getMetrics(): PerformanceMetrics;
    /**
     * 重置性能指标
     */
    reset(): void;
    /**
     * 生成性能报告
     * @returns string 性能报告
     */
    generateReport(): string;
}
//# sourceMappingURL=performance.d.ts.map