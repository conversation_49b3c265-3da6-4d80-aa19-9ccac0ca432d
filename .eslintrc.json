{"env": {"es2022": true, "node": true}, "extends": ["eslint:recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "plugins": ["@typescript-eslint"], "rules": {"@typescript-eslint/no-unused-vars": "error", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/no-non-null-assertion": "warn", "prefer-const": "error", "no-var": "error", "no-console": "off"}}