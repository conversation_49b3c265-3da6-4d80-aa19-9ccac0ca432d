{"version": 3, "file": "performance.js", "sourceRoot": "", "sources": ["../../src/utils/performance.ts"], "names": [], "mappings": "AAAA;;;GAGG;AAkBH,MAAM,OAAO,kBAAkB;IACrB,OAAO,CAAqB;IAC5B,SAAS,CAAS;IAClB,UAAU,GAAa,EAAE,CAAC;IAC1B,kBAAkB,CAAS;IAEnC,YAAY,qBAA6B,IAAI;QAC3C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC5B,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;QAC7C,IAAI,CAAC,OAAO,GAAG;YACb,UAAU,EAAE,CAAC;YACb,gBAAgB,EAAE,CAAC;YACnB,cAAc,EAAE,CAAC;YACjB,UAAU,EAAE,CAAC;YACb,eAAe,EAAE,CAAC;YAClB,WAAW,EAAE,OAAO,CAAC,WAAW,EAAE;YAClC,MAAM,EAAE,CAAC;SACV,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACH,WAAW,CAAC,aAAqB,EAAE,UAAmB,IAAI;QACxD,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;QAC1B,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAEpC,IAAI,aAAa,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC5C,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAChC,CAAC;QAED,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;QAC5B,CAAC;QAED,WAAW;QACX,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;IACjC,CAAC;IAED;;OAEG;IACK,sBAAsB;QAC5B,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC/B,MAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;YACvD,IAAI,CAAC,OAAO,CAAC,gBAAgB,GAAG,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;QAC/D,CAAC;IACH,CAAC;IAED;;;OAGG;IACH,UAAU;QACR,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC;QAClD,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QACjD,OAAO,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,KAAK;QACH,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC5B,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QACrB,IAAI,CAAC,OAAO,GAAG;YACb,UAAU,EAAE,CAAC;YACb,gBAAgB,EAAE,CAAC;YACnB,cAAc,EAAE,CAAC;YACjB,UAAU,EAAE,CAAC;YACb,eAAe,EAAE,CAAC;YAClB,WAAW,EAAE,OAAO,CAAC,WAAW,EAAE;YAClC,MAAM,EAAE,CAAC;SACV,CAAC;IACJ,CAAC;IAED;;;OAGG;IACH,cAAc;QACZ,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QAClC,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC;QACzD,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,MAAM,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC;QAElE,OAAO;;QAEH,aAAa,IAAI,aAAa;QAC9B,OAAO,CAAC,UAAU;UAChB,OAAO,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC;QACrC,OAAO,CAAC,cAAc;OACvB,OAAO,CAAC,UAAU;OAClB,OAAO,CAAC,eAAe;;WAEnB,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;iBAC5C,CAAC,OAAO,CAAC,WAAW,CAAC,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;kBACtD,CAAC,OAAO,CAAC,WAAW,CAAC,SAAS,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;;KAErE,CAAC,IAAI,EAAE,CAAC;IACX,CAAC;CACF"}