/**
 * 性能监控工具
 * 用于监控MySQL MCP服务器的性能指标
 */

export interface PerformanceMetrics {
  queryCount: number;
  averageQueryTime: number;
  slowQueryCount: number;
  errorCount: number;
  connectionCount: number;
  memoryUsage: {
    rss: number;
    heapTotal: number;
    heapUsed: number;
    external: number;
    arrayBuffers: number;
  };
  uptime: number;
}

export class PerformanceMonitor {
  private metrics: PerformanceMetrics;
  private startTime: number;
  private queryTimes: number[] = [];
  private slowQueryThreshold: number;

  constructor(slowQueryThreshold: number = 1000) {
    this.startTime = Date.now();
    this.slowQueryThreshold = slowQueryThreshold;
    this.metrics = {
      queryCount: 0,
      averageQueryTime: 0,
      slowQueryCount: 0,
      errorCount: 0,
      connectionCount: 0,
      memoryUsage: process.memoryUsage(),
      uptime: 0
    };
  }

  /**
   * 记录查询执行
   * @param executionTime 执行时间（毫秒）
   * @param success 是否成功
   */
  recordQuery(executionTime: number, success: boolean = true): void {
    this.metrics.queryCount++;
    this.queryTimes.push(executionTime);
    
    if (executionTime > this.slowQueryThreshold) {
      this.metrics.slowQueryCount++;
    }
    
    if (!success) {
      this.metrics.errorCount++;
    }
    
    // 更新平均查询时间
    this.updateAverageQueryTime();
  }

  /**
   * 记录连接获取
   */
  recordConnection(): void {
    this.metrics.connectionCount++;
  }

  /**
   * 更新平均查询时间
   */
  private updateAverageQueryTime(): void {
    if (this.queryTimes.length > 0) {
      const sum = this.queryTimes.reduce((a, b) => a + b, 0);
      this.metrics.averageQueryTime = sum / this.queryTimes.length;
    }
  }

  /**
   * 获取当前性能指标
   * @returns PerformanceMetrics 性能指标
   */
  getMetrics(): PerformanceMetrics {
    this.metrics.uptime = Date.now() - this.startTime;
    this.metrics.memoryUsage = process.memoryUsage();
    return { ...this.metrics };
  }

  /**
   * 重置性能指标
   */
  reset(): void {
    this.startTime = Date.now();
    this.queryTimes = [];
    this.metrics = {
      queryCount: 0,
      averageQueryTime: 0,
      slowQueryCount: 0,
      errorCount: 0,
      connectionCount: 0,
      memoryUsage: process.memoryUsage(),
      uptime: 0
    };
  }

  /**
   * 生成性能报告
   * @returns string 性能报告
   */
  generateReport(): string {
    const metrics = this.getMetrics();
    const uptimeMinutes = Math.floor(metrics.uptime / 60000);
    const uptimeSeconds = Math.floor((metrics.uptime % 60000) / 1000);
    
    return `
=== 性能监控报告 ===
运行时间: ${uptimeMinutes}分${uptimeSeconds}秒
总查询数: ${metrics.queryCount}
平均查询时间: ${metrics.averageQueryTime.toFixed(2)}ms
慢查询数: ${metrics.slowQueryCount}
错误数: ${metrics.errorCount}
连接数: ${metrics.connectionCount}
内存使用:
  - RSS: ${(metrics.memoryUsage.rss / 1024 / 1024).toFixed(2)}MB
  - Heap Used: ${(metrics.memoryUsage.heapUsed / 1024 / 1024).toFixed(2)}MB
  - Heap Total: ${(metrics.memoryUsage.heapTotal / 1024 / 1024).toFixed(2)}MB
==================
    `.trim();
  }
} 