#!/usr/bin/env node
/**
 * MySQL MCP服务器 - 为Model Context Protocol提供MySQL数据库操作功能
 * 支持安全的数据库查询、更新、插入和删除操作
 * 包含频率限制、输入验证、SQL注入防护等安全功能
 *
 * <AUTHOR>
 * @version 1.1.0
 */
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { CallToolRequestSchema, ListToolsRequestSchema, } from '@modelcontextprotocol/sdk/types.js';
import mysql from 'mysql2/promise';
import crypto from 'crypto';
import { EventEmitter } from 'events';
/**
 * 日志级别枚举
 */
var LogLevel;
(function (LogLevel) {
    LogLevel["DEBUG"] = "DEBUG";
    LogLevel["INFO"] = "INFO";
    LogLevel["WARN"] = "WARN";
    LogLevel["ERROR"] = "ERROR";
    LogLevel["SECURITY"] = "SECURITY";
})(LogLevel || (LogLevel = {}));
/**
 * 自定义错误类
 */
class MySQLMCPError extends Error {
    code;
    details;
    constructor(message, code, details) {
        super(message);
        this.code = code;
        this.details = details;
        this.name = 'MySQLMCPError';
    }
}
class SecurityError extends MySQLMCPError {
    constructor(message, details) {
        super(message, 'SECURITY_ERROR', details);
        this.name = 'SecurityError';
    }
}
class ValidationError extends MySQLMCPError {
    constructor(message, field) {
        super(message, 'VALIDATION_ERROR', { field });
        this.name = 'ValidationError';
    }
}
class DatabaseError extends MySQLMCPError {
    constructor(message, originalError) {
        super(message, 'DATABASE_ERROR', { originalError: originalError?.message });
        this.name = 'DatabaseError';
    }
}
class RateLimitError extends MySQLMCPError {
    constructor(message, retryAfter) {
        super(message, 'RATE_LIMIT_ERROR', { retryAfter });
        this.name = 'RateLimitError';
    }
}
/**
 * MySQL MCP服务器类
 * 提供安全的MySQL数据库操作功能，包括：
 * - 数据查询、插入、更新、删除
 * - 表结构查询和管理
 * - 索引和外键信息查询
 * - 安全控制：频率限制、输入验证、SQL注入防护
 */
class MySQLMCPServer extends EventEmitter {
    server;
    pool = null;
    config;
    securityConfig;
    rateLimitMap = new Map();
    sessionId;
    isShuttingDown = false;
    connectionRetryCount = 0;
    maxConnectionRetries = 3;
    /**
     * 初始化MCP服务器和配置
     */
    constructor() {
        super();
        // 生成唯一会话标识符，用于追踪和日志记录
        this.sessionId = crypto.randomUUID();
        // 初始化MCP服务器
        this.server = new Server({
            name: 'mysql-mcp-server',
            version: '1.1.0',
        }, {
            capabilities: {
                tools: {}, // 声明支持工具功能
            },
        });
        // 从环境变量中加载MySQL配置
        this.config = this.loadMySQLConfig();
        // 加载安全配置，设置防护措施
        this.securityConfig = this.loadSecurityConfig();
        // 设置请求处理器
        this.setupHandlers();
        // 设置优雅关闭处理
        this.setupGracefulShutdown();
    }
    /**
     * 加载MySQL配置
     * @returns MySQLConfig 配置对象
     */
    loadMySQLConfig() {
        return {
            host: process.env.MYSQL_HOST || 'localhost',
            port: parseInt(process.env.MYSQL_PORT || '3306'),
            user: process.env.MYSQL_USER || 'root',
            password: process.env.MYSQL_PASSWORD || '',
            database: process.env.MYSQL_DATABASE || '',
            connectionLimit: parseInt(process.env.MYSQL_CONNECTION_LIMIT || '10'),
            connectTimeout: parseInt(process.env.MYSQL_CONNECT_TIMEOUT || '60000'),
            idleTimeout: parseInt(process.env.MYSQL_IDLE_TIMEOUT || '60000'),
            ssl: process.env.MYSQL_SSL === 'true',
            charset: process.env.MYSQL_CHARSET || 'utf8mb4',
            timezone: process.env.MYSQL_TIMEZONE || 'local',
        };
    }
    /**
     * 加载安全配置
     * @returns SecurityConfig 安全配置对象
     */
    loadSecurityConfig() {
        return {
            maxQueryLength: parseInt(process.env.MAX_QUERY_LENGTH || '10000'),
            allowedQueryTypes: (process.env.ALLOWED_QUERY_TYPES || 'SELECT,SHOW,DESCRIBE,INSERT,UPDATE,DELETE,CREATE,DROP,ALTER').split(',').map(s => s.trim()),
            maxResultRows: parseInt(process.env.MAX_RESULT_ROWS || '1000'),
            queryTimeout: parseInt(process.env.QUERY_TIMEOUT || '30000'),
            rateLimitWindow: parseInt(process.env.RATE_LIMIT_WINDOW || '60000'),
            rateLimitMax: parseInt(process.env.RATE_LIMIT_MAX || '100'),
            enableAuditLog: process.env.ENABLE_AUDIT_LOG !== 'false',
            blockDangerousQueries: process.env.BLOCK_DANGEROUS_QUERIES !== 'false',
        };
    }
    /**
     * 设置优雅关闭处理
     */
    setupGracefulShutdown() {
        const shutdown = async (signal) => {
            if (this.isShuttingDown)
                return;
            this.isShuttingDown = true;
            this.log(LogLevel.INFO, `Received ${signal}, starting graceful shutdown`);
            try {
                await this.close();
                this.log(LogLevel.INFO, 'Graceful shutdown completed');
                process.exit(0);
            }
            catch (error) {
                this.log(LogLevel.ERROR, 'Error during shutdown', { error: error instanceof Error ? error.message : String(error) });
                process.exit(1);
            }
        };
        process.on('SIGINT', () => shutdown('SIGINT'));
        process.on('SIGTERM', () => shutdown('SIGTERM'));
        process.on('uncaughtException', (error) => {
            this.log(LogLevel.ERROR, 'Uncaught exception', { error: error.message });
            shutdown('uncaughtException');
        });
        process.on('unhandledRejection', (reason) => {
            this.log(LogLevel.ERROR, 'Unhandled rejection', { reason: String(reason) });
            shutdown('unhandledRejection');
        });
    }
    /**
     * 获取MySQL数据库连接
     * 使用连接池来管理数据库连接，提高性能和稳定性
     * @returns Promise<mysql.PoolConnection> 数据库连接对象
     */
    async getConnection() {
        if (!this.pool) {
            this.pool = this.createConnectionPool();
        }
        try {
            const connection = await this.pool.getConnection();
            this.connectionRetryCount = 0; // 重置重试计数
            return connection;
        }
        catch (error) {
            this.connectionRetryCount++;
            this.log(LogLevel.ERROR, 'Failed to get database connection', {
                error: error instanceof Error ? error.message : String(error),
                retryCount: this.connectionRetryCount
            });
            // 如果重试次数未超过限制，尝试重新创建连接池
            if (this.connectionRetryCount < this.maxConnectionRetries) {
                this.log(LogLevel.WARN, 'Retrying database connection', { retryCount: this.connectionRetryCount });
                this.pool = this.createConnectionPool();
                return this.getConnection();
            }
            throw new DatabaseError('Failed to acquire database connection after multiple retries', error instanceof Error ? error : new Error(String(error)));
        }
    }
    /**
     * 创建数据库连接池
     * @returns mysql.Pool 连接池对象
     */
    createConnectionPool() {
        const poolConfig = {
            host: this.config.host,
            port: this.config.port,
            user: this.config.user,
            password: this.config.password,
            database: this.config.database,
            // 性能优化配置
            multipleStatements: false, // 禁止多语句执行，增强安全性
            dateStrings: false, // 使用Date对象而不是字符串
            supportBigNumbers: true, // 支持大数字
            bigNumberStrings: false, // 大数字作为数字返回
            trace: false, // 禁用堆栈跟踪，提高性能
            // 连接池优化配置
            queueLimit: 0, // 无排队限制，防止请求积压
            maxIdle: 5, // 最大空闲连接数
            idleTimeout: 300000, // 空闲连接超时时间（5分钟）
            enableKeepAlive: true, // 启用TCP keep-alive
            keepAliveInitialDelay: 0, // keep-alive初始延迟
        };
        // 有条件地添加可选配置项
        if (this.config.connectionLimit !== undefined) {
            poolConfig.connectionLimit = this.config.connectionLimit;
        }
        if (this.config.connectTimeout !== undefined) {
            poolConfig.connectTimeout = this.config.connectTimeout;
        }
        if (this.config.idleTimeout !== undefined) {
            poolConfig.idleTimeout = this.config.idleTimeout;
        }
        if (this.config.charset && this.config.charset.trim() !== '') {
            poolConfig.charset = this.config.charset;
        }
        if (this.config.timezone && this.config.timezone.trim() !== '') {
            poolConfig.timezone = this.config.timezone;
        }
        if (this.config.ssl) {
            poolConfig.ssl = { rejectUnauthorized: false };
        }
        const pool = mysql.createPool(poolConfig);
        pool.on('connection', (connection) => {
            this.log(LogLevel.INFO, 'New database connection established');
            // 添加错误事件监听器，防止未捕获的错误导致程序崩溃
            connection.on('error', (err) => {
                this.log(LogLevel.ERROR, 'Database connection error', { error: err.message });
            });
        });
        pool.on('acquire', () => {
            this.log(LogLevel.DEBUG, 'Connection acquired from pool');
        });
        pool.on('release', () => {
            this.log(LogLevel.DEBUG, 'Connection released to pool');
        });
        return pool;
    }
    /**
     * 执行带超时控制的查询
     * @param connection 数据库连接
     * @param query SQL查询语句
     * @param params 查询参数
     * @returns Promise<QueryResult> 查询结果
     */
    async executeQueryWithTimeout(connection, query, params = []) {
        const startTime = Date.now();
        try {
            const result = await Promise.race([
                connection.execute(query, params),
                new Promise((_, reject) => setTimeout(() => reject(new DatabaseError('Query timeout')), this.securityConfig.queryTimeout))
            ]);
            const executionTime = Date.now() - startTime;
            // 记录慢查询
            if (executionTime > 5000) { // 5秒以上的查询记录为慢查询
                this.log(LogLevel.WARN, 'Slow query detected', {
                    query: query.substring(0, 100), // 只记录前100个字符
                    executionTime,
                    paramsCount: params.length
                });
            }
            // 发出查询完成事件
            this.emit('queryCompleted', {
                query: query.substring(0, 100),
                executionTime,
                paramsCount: params.length,
                success: true
            });
            return result;
        }
        catch (error) {
            const executionTime = Date.now() - startTime;
            this.log(LogLevel.ERROR, 'Query execution failed', {
                query: query.substring(0, 100),
                executionTime,
                error: error instanceof Error ? error.message : String(error)
            });
            // 发出查询失败事件
            this.emit('queryFailed', {
                query: query.substring(0, 100),
                executionTime,
                error: error instanceof Error ? error.message : String(error)
            });
            throw error instanceof Error ? new DatabaseError(error.message, error) : new DatabaseError(String(error));
        }
    }
    /**
     * 执行查询操作，返回行数据
     * @param connection 数据库连接
     * @param query SQL查询语句
     * @param params 查询参数
     * @returns Promise<{ rows: mysql.RowDataPacket[] | mysql.RowDataPacket[][]; fields?: mysql.FieldPacket[]; }> 查询结果
     */
    async executeQuery(connection, query, params = []) {
        const [rows, fields] = await this.executeQueryWithTimeout(connection, query, params);
        return { rows, fields };
    }
    /**
     * 执行修改操作（INSERT, UPDATE, DELETE等）
     * @param connection 数据库连接
     * @param query SQL语句
     * @param params 查询参数
     * @returns Promise<{ result: mysql.OkPacket | mysql.OkPacket[] | mysql.ResultSetHeader; fields?: mysql.FieldPacket[]; }> 执行结果
     */
    async executeModification(connection, query, params = []) {
        const [result, fields] = await this.executeQueryWithTimeout(connection, query, params);
        return { result, fields };
    }
    /**
     * 构建查询响应内容
     * @param data 查询结果数据
     * @returns 标准化的响应对象
     */
    buildQueryResponse(data) {
        return {
            content: [{
                    type: 'text',
                    text: JSON.stringify(data, null, 2),
                }],
        };
    }
    /**
     * 构建成功响应内容
     * @param result 操作结果
     * @param executionTime 执行时间
     * @returns 标准化的成功响应对象
     */
    buildSuccessResponse(result, executionTime = 0) {
        const response = {
            success: true,
            result,
            executionTime
        };
        // 如果是修改操作，添加影响行数信息
        if (result && typeof result === 'object' && 'affectedRows' in result) {
            response.affectedRows = result.affectedRows;
        }
        // 如果是插入操作，添加插入ID信息
        if (result && typeof result === 'object' && 'insertId' in result) {
            response.insertId = result.insertId;
        }
        return {
            content: [{
                    type: 'text',
                    text: JSON.stringify(response, null, 2),
                }],
        };
    }
    /**
     * 验证用户输入的安全性
     * @param input 用户输入的数据
     * @param fieldName 字段名称，用于错误消息
     */
    validateInput(input, fieldName) {
        if (typeof input === 'string') {
            // 检查空字节注入攻击
            if (input.includes('\0')) {
                throw new Error(`Invalid character in ${fieldName}`);
            }
            // 检查输入长度，防止过长输入导致的攻击
            if (input.length > 1000) {
                throw new Error(`${fieldName} exceeds maximum length`);
            }
        }
    }
    /**
     * 验证SQL查询的安全性，防止SQL注入和危险操作
     * @param query SQL查询语句
     */
    validateQuery(query) {
        // 检查查询长度，防止过长查询导致的DoS攻击
        if (query.length > this.securityConfig.maxQueryLength) {
            throw new SecurityError('Query exceeds maximum allowed length', { queryLength: query.length });
        }
        // 检查空查询
        if (!query.trim()) {
            throw new ValidationError('Query cannot be empty');
        }
        // 使用预编译的正则表达式优化性能
        const dangerousPatterns = this.getDangerousPatterns();
        for (const pattern of dangerousPatterns) {
            if (pattern.test(query)) {
                if (this.securityConfig.blockDangerousQueries) {
                    throw new SecurityError('Query contains prohibited operations', { pattern: pattern.source });
                }
                else {
                    this.log(LogLevel.SECURITY, 'Dangerous query pattern detected', { pattern: pattern.source, query: query.substring(0, 100) });
                }
            }
        }
        // 验证查询类型，只允许白名单中的操作
        const queryType = this.extractQueryType(query);
        if (queryType && !this.securityConfig.allowedQueryTypes.includes(queryType)) {
            throw new SecurityError(`Query type '${queryType}' is not allowed`, { queryType });
        }
        // 检查注释和特殊字符
        if (query.includes('--') || query.includes('/*') || query.includes('*/')) {
            this.log(LogLevel.SECURITY, 'Query contains comments', { query: query.substring(0, 100) });
        }
    }
    /**
     * 获取危险操作模式列表（缓存以提高性能）
     */
    static dangerousPatternsCache = null;
    getDangerousPatterns() {
        if (!MySQLMCPServer.dangerousPatternsCache) {
            MySQLMCPServer.dangerousPatternsCache = [
                /\b(LOAD_FILE|INTO OUTFILE|INTO DUMPFILE)\b/i, // 文件操作
                /\b(SYSTEM|EXEC|SHELL)\b/i, // 系统命令执行
                /\bINTO\s+OUTFILE\b/i, // 文件输出
                /\bLOAD\s+DATA\b/i, // 数据加载
            ];
        }
        return MySQLMCPServer.dangerousPatternsCache;
    }
    /**
     * 提取查询类型
     * @param query SQL查询语句
     * @returns 查询类型
     */
    extractQueryType(query) {
        return query.trim().split(/\s+/)[0]?.toUpperCase();
    }
    /**
     * 验证表名的合法性，防止SQL注入
     * @param tableName 表名
     */
    validateTableName(tableName) {
        // 只允许字母、数字、下划线和短横线，防止SQL注入
        if (!/^[a-zA-Z0-9_-]+$/.test(tableName)) {
            throw new ValidationError('Invalid table name format', 'table_name');
        }
        // 检查表名长度，MySQL表名最大长度为64字符
        if (tableName.length > 64) {
            throw new ValidationError('Table name exceeds maximum length', 'table_name');
        }
    }
    /**
     * 清理频率限制记录，定期删除过期的记录以节省内存
     */
    cleanupRateLimit() {
        const now = Date.now();
        const expiredEntries = [];
        for (const [key, entry] of this.rateLimitMap.entries()) {
            if (now > entry.resetTime) {
                expiredEntries.push(key);
            }
        }
        // 批量删除过期记录
        for (const key of expiredEntries) {
            this.rateLimitMap.delete(key);
        }
    }
    /**
     * 检查请求频率限制，防止恶意请求和DoS攻击
     * @param identifier 请求标识符，用于区分不同的请求源
     */
    checkRateLimit(identifier = 'default') {
        // 定期清理过期记录（每100次请求清理一次）
        if (Math.random() < 0.01) {
            this.cleanupRateLimit();
        }
        const now = Date.now();
        const entry = this.rateLimitMap.get(identifier);
        // 如果没有记录或时间窗口已过期，重置计数器
        if (!entry || now > entry.resetTime) {
            this.rateLimitMap.set(identifier, {
                count: 1,
                resetTime: now + this.securityConfig.rateLimitWindow,
                lastRequestTime: now
            });
            return;
        }
        // 检查请求间隔（防止过于频繁的请求）
        const timeSinceLastRequest = now - entry.lastRequestTime;
        if (timeSinceLastRequest < 100) { // 最小100ms间隔
            throw new RateLimitError('Request too frequent', 100 - timeSinceLastRequest);
        }
        // 检查是否超过频率限制
        if (entry.count >= this.securityConfig.rateLimitMax) {
            const retryAfter = entry.resetTime - now;
            throw new RateLimitError('Rate limit exceeded. Please try again later.', retryAfter);
        }
        // 增加请求计数并更新最后请求时间
        entry.count++;
        entry.lastRequestTime = now;
    }
    /**
     * 统一的日志记录方法
     * @param level 日志级别
     * @param message 日志消息
     * @param context 上下文信息
     */
    log(level, message, context) {
        const logEntry = {
            timestamp: new Date().toISOString(),
            level,
            sessionId: this.sessionId,
            message,
            ...(context && { context })
        };
        // 根据日志级别选择输出方式
        const output = level === LogLevel.SECURITY || level === LogLevel.ERROR ? console.error : console.log;
        // 只在启用审计日志时记录安全事件
        if (level === LogLevel.SECURITY && !this.securityConfig.enableAuditLog) {
            return;
        }
        // 格式化输出
        const formattedLog = `[${level}] ${JSON.stringify(logEntry)}`;
        output(formattedLog);
        // 发出日志事件
        this.emit('log', logEntry);
    }
    /**
     * 记录安全事件，用于安全监控和审计
     * @param event 事件类型
     * @param details 事件详情
     */
    logSecurityEvent(event, details) {
        this.log(LogLevel.SECURITY, `Security event: ${event}`, details);
    }
    /**
     * 清理错误信息中的敏感信息，防止信息泄露
     * @param error 原始错误对象
     * @returns string 清理后的错误信息
     */
    sanitizeError(error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        // 移除错误信息中的敏感信息（密码、主机、用户名）
        return errorMessage
            .replace(/password[^\s]*/gi, 'password=***')
            .replace(/host[^\s]*/gi, 'host=***')
            .replace(/user[^\s]*/gi, 'user=***')
            .replace(/([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/g, '***@***.***'); // 隐藏邮箱
    }
    /**
     * 构建错误响应
     * @param error 错误对象
     * @param context 错误上下文信息
     * @returns 错误响应对象
     */
    buildErrorResponse(error, context) {
        const sanitizedMessage = this.sanitizeError(error);
        // 记录错误日志
        this.log(LogLevel.ERROR, 'Operation failed', {
            error: sanitizedMessage,
            type: error instanceof Error ? error.constructor.name : typeof error,
            ...context
        });
        return {
            content: [{
                    type: 'text',
                    text: `Error: ${sanitizedMessage}`,
                }],
            isError: true,
        };
    }
    /**
     * 设置请求处理器，处理MCP工具列表和工具调用
     */
    setupHandlers() {
        // 处理工具列表请求，返回所有可用的MySQL操作工具
        this.server.setRequestHandler(ListToolsRequestSchema, async () => {
            return {
                tools: [
                    {
                        name: 'mysql_query',
                        description: 'Execute a MySQL query (SELECT, SHOW, DESCRIBE, etc.)',
                        inputSchema: {
                            type: 'object',
                            properties: {
                                query: {
                                    type: 'string',
                                    description: 'The SQL query to execute',
                                },
                                params: {
                                    type: 'array',
                                    items: { type: 'string' },
                                    description: 'Optional parameters for prepared statements',
                                    default: [],
                                },
                            },
                            required: ['query'],
                        },
                    },
                    {
                        name: 'mysql_show_tables',
                        description: 'Show all tables in the current database',
                        inputSchema: {
                            type: 'object',
                            properties: {},
                            required: [],
                        },
                    },
                    {
                        name: 'mysql_describe_table',
                        description: 'Describe the structure of a specific table',
                        inputSchema: {
                            type: 'object',
                            properties: {
                                table_name: {
                                    type: 'string',
                                    description: 'Name of the table to describe',
                                },
                            },
                            required: ['table_name'],
                        },
                    },
                    {
                        name: 'mysql_select_data',
                        description: 'Select data from a table with optional conditions',
                        inputSchema: {
                            type: 'object',
                            properties: {
                                table_name: {
                                    type: 'string',
                                    description: 'Name of the table to select from',
                                },
                                columns: {
                                    type: 'array',
                                    items: { type: 'string' },
                                    description: 'Columns to select (default: all columns)',
                                    default: ['*'],
                                },
                                where_clause: {
                                    type: 'string',
                                    description: 'Optional WHERE clause (without WHERE keyword)',
                                },
                                limit: {
                                    type: 'number',
                                    description: 'Optional LIMIT for results',
                                },
                            },
                            required: ['table_name'],
                        },
                    },
                    {
                        name: 'mysql_insert_data',
                        description: 'Insert new data into a table',
                        inputSchema: {
                            type: 'object',
                            properties: {
                                table_name: {
                                    type: 'string',
                                    description: 'Name of the table to insert into',
                                },
                                data: {
                                    type: 'object',
                                    description: 'Key-value pairs of column names and values to insert',
                                },
                            },
                            required: ['table_name', 'data'],
                        },
                    },
                    {
                        name: 'mysql_update_data',
                        description: 'Update existing data in a table',
                        inputSchema: {
                            type: 'object',
                            properties: {
                                table_name: {
                                    type: 'string',
                                    description: 'Name of the table to update',
                                },
                                data: {
                                    type: 'object',
                                    description: 'Key-value pairs of column names and new values',
                                },
                                where_clause: {
                                    type: 'string',
                                    description: 'WHERE clause to specify which records to update (without WHERE keyword)',
                                },
                            },
                            required: ['table_name', 'data', 'where_clause'],
                        },
                    },
                    {
                        name: 'mysql_delete_data',
                        description: 'Delete data from a table',
                        inputSchema: {
                            type: 'object',
                            properties: {
                                table_name: {
                                    type: 'string',
                                    description: 'Name of the table to delete from',
                                },
                                where_clause: {
                                    type: 'string',
                                    description: 'WHERE clause to specify which records to delete (without WHERE keyword)',
                                },
                            },
                            required: ['table_name', 'where_clause'],
                        },
                    },
                    {
                        name: 'mysql_get_schema',
                        description: 'Get database schema information including tables, columns, and constraints',
                        inputSchema: {
                            type: 'object',
                            properties: {
                                table_name: {
                                    type: 'string',
                                    description: 'Optional specific table name to get schema for',
                                },
                            },
                            required: [],
                        },
                    },
                    {
                        name: 'mysql_get_indexes',
                        description: 'Get index information for a table or all tables',
                        inputSchema: {
                            type: 'object',
                            properties: {
                                table_name: {
                                    type: 'string',
                                    description: 'Optional specific table name to get indexes for',
                                },
                            },
                            required: [],
                        },
                    },
                    {
                        name: 'mysql_get_foreign_keys',
                        description: 'Get foreign key constraints for a table or all tables',
                        inputSchema: {
                            type: 'object',
                            properties: {
                                table_name: {
                                    type: 'string',
                                    description: 'Optional specific table name to get foreign keys for',
                                },
                            },
                            required: [],
                        },
                    },
                    {
                        name: 'mysql_create_table',
                        description: 'Create a new table with specified columns and constraints',
                        inputSchema: {
                            type: 'object',
                            properties: {
                                table_name: {
                                    type: 'string',
                                    description: 'Name of the table to create',
                                },
                                columns: {
                                    type: 'array',
                                    items: {
                                        type: 'object',
                                        properties: {
                                            name: { type: 'string' },
                                            type: { type: 'string' },
                                            nullable: { type: 'boolean', default: true },
                                            default: { type: 'string' },
                                            primary_key: { type: 'boolean', default: false },
                                            auto_increment: { type: 'boolean', default: false },
                                        },
                                        required: ['name', 'type'],
                                    },
                                    description: 'Array of column definitions',
                                },
                            },
                            required: ['table_name', 'columns'],
                        },
                    },
                    {
                        name: 'mysql_drop_table',
                        description: 'Drop (delete) a table from the database',
                        inputSchema: {
                            type: 'object',
                            properties: {
                                table_name: {
                                    type: 'string',
                                    description: 'Name of the table to drop',
                                },
                                if_exists: {
                                    type: 'boolean',
                                    description: 'Use IF EXISTS clause to avoid errors if table does not exist',
                                    default: true,
                                },
                            },
                            required: ['table_name'],
                        },
                    },
                ],
            };
        });
        // 处理工具调用请求，执行具体的MySQL操作
        this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
            const { name, arguments: args } = request.params;
            let connection = null;
            try {
                // 检查请求频率限制，防止滥用
                this.checkRateLimit();
                // 验证输入参数的安全性
                if (args) {
                    Object.entries(args).forEach(([key, value]) => {
                        this.validateInput(value, key);
                    });
                }
                // 获取数据库连接
                connection = await this.getConnection();
                // 根据工具名称执行相应操作
                switch (name) {
                    case 'mysql_query': {
                        const { query, params = [] } = args;
                        this.validateQuery(query);
                        const queryResult = await this.executeQuery(connection, query, params);
                        const limitedRows = Array.isArray(queryResult.rows) ? queryResult.rows.slice(0, this.securityConfig.maxResultRows) : queryResult.rows;
                        return this.buildQueryResponse(limitedRows);
                    }
                    case 'mysql_show_tables': {
                        const queryResult = await this.executeQuery(connection, 'SHOW TABLES');
                        return this.buildQueryResponse(queryResult.rows);
                    }
                    case 'mysql_describe_table': {
                        const { table_name } = args;
                        this.validateTableName(table_name);
                        const queryResult = await this.executeQuery(connection, `DESCRIBE \`${table_name}\``);
                        return this.buildQueryResponse(queryResult.rows);
                    }
                    case 'mysql_select_data': {
                        const { table_name, columns = ['*'], where_clause, limit } = args;
                        let query = `SELECT ${columns.join(', ')} FROM \`${table_name}\``;
                        if (where_clause) {
                            query += ` WHERE ${where_clause}`;
                        }
                        if (limit) {
                            query += ` LIMIT ${limit}`;
                        }
                        const [rows] = await connection.execute(query);
                        return this.buildQueryResponse(rows);
                    }
                    case 'mysql_insert_data': {
                        const { table_name, data } = args;
                        this.validateTableName(table_name);
                        const columns = Object.keys(data);
                        const values = Object.values(data);
                        const placeholders = new Array(values.length).fill('?').join(', ');
                        const startTime = Date.now();
                        const query = `INSERT INTO \`${table_name}\` (\`${columns.join('`, `')}\`) VALUES (${placeholders})`;
                        const executeResult = await this.executeModification(connection, query, values);
                        const executionTime = Date.now() - startTime;
                        return this.buildSuccessResponse(executeResult.result, executionTime);
                    }
                    case 'mysql_update_data': {
                        const { table_name, data, where_clause } = args;
                        this.validateTableName(table_name);
                        const columns = Object.keys(data);
                        const values = Object.values(data);
                        const setClause = columns.map(col => `\`${col}\` = ?`).join(', ');
                        const startTime = Date.now();
                        const query = `UPDATE \`${table_name}\` SET ${setClause} WHERE ${where_clause}`;
                        const executeResult = await this.executeModification(connection, query, values);
                        const executionTime = Date.now() - startTime;
                        return this.buildSuccessResponse(executeResult.result, executionTime);
                    }
                    case 'mysql_delete_data': {
                        const { table_name, where_clause } = args;
                        this.validateTableName(table_name);
                        const startTime = Date.now();
                        const query = `DELETE FROM \`${table_name}\` WHERE ${where_clause}`;
                        const executeResult = await this.executeModification(connection, query);
                        const executionTime = Date.now() - startTime;
                        return this.buildSuccessResponse(executeResult.result, executionTime);
                    }
                    case 'mysql_get_schema': {
                        const { table_name } = args;
                        let query = `
              SELECT 
                TABLE_NAME,
                COLUMN_NAME,
                DATA_TYPE,
                IS_NULLABLE,
                COLUMN_DEFAULT,
                COLUMN_KEY,
                EXTRA,
                COLUMN_COMMENT
              FROM INFORMATION_SCHEMA.COLUMNS 
              WHERE TABLE_SCHEMA = DATABASE()
            `;
                        const params = [];
                        if (table_name) {
                            this.validateTableName(table_name);
                            query += ` AND TABLE_NAME = ?`;
                            params.push(table_name);
                        }
                        query += ' ORDER BY TABLE_NAME, ORDINAL_POSITION';
                        const queryResult = await this.executeQuery(connection, query, params);
                        return this.buildQueryResponse(queryResult.rows);
                    }
                    case 'mysql_get_indexes': {
                        const { table_name } = args;
                        let query = `
              SELECT 
                TABLE_NAME,
                INDEX_NAME,
                COLUMN_NAME,
                NON_UNIQUE,
                SEQ_IN_INDEX,
                INDEX_TYPE
              FROM INFORMATION_SCHEMA.STATISTICS 
              WHERE TABLE_SCHEMA = DATABASE()
            `;
                        const params = [];
                        if (table_name) {
                            this.validateTableName(table_name);
                            query += ` AND TABLE_NAME = ?`;
                            params.push(table_name);
                        }
                        query += ' ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX';
                        const queryResult = await this.executeQuery(connection, query, params);
                        return this.buildQueryResponse(queryResult.rows);
                    }
                    case 'mysql_get_foreign_keys': {
                        const { table_name } = args;
                        let query = `
              SELECT 
                TABLE_NAME,
                COLUMN_NAME,
                CONSTRAINT_NAME,
                REFERENCED_TABLE_NAME,
                REFERENCED_COLUMN_NAME,
                UPDATE_RULE,
                DELETE_RULE
              FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
              WHERE TABLE_SCHEMA = DATABASE() 
                AND REFERENCED_TABLE_NAME IS NOT NULL
            `;
                        const params = [];
                        if (table_name) {
                            this.validateTableName(table_name);
                            query += ` AND TABLE_NAME = ?`;
                            params.push(table_name);
                        }
                        query += ' ORDER BY TABLE_NAME, CONSTRAINT_NAME';
                        const queryResult = await this.executeQuery(connection, query, params);
                        return this.buildQueryResponse(queryResult.rows);
                    }
                    case 'mysql_create_table': {
                        const { table_name, columns } = args;
                        this.validateTableName(table_name);
                        const columnDefs = columns.map(col => {
                            let def = `\`${col.name}\` ${col.type}`;
                            if (!col.nullable)
                                def += ' NOT NULL';
                            if (col.auto_increment)
                                def += ' AUTO_INCREMENT';
                            if (col.default)
                                def += ` DEFAULT ${col.default}`;
                            return def;
                        });
                        const primaryKeys = columns.filter(col => col.primary_key).map(col => col.name);
                        if (primaryKeys.length > 0) {
                            columnDefs.push(`PRIMARY KEY (\`${primaryKeys.join('`, `')}\`)`);
                        }
                        const startTime = Date.now();
                        const query = `CREATE TABLE \`${table_name}\` (${columnDefs.join(', ')})`;
                        const executeResult = await this.executeModification(connection, query);
                        const executionTime = Date.now() - startTime;
                        return this.buildSuccessResponse(executeResult.result, executionTime);
                    }
                    case 'mysql_drop_table': {
                        const { table_name, if_exists = true } = args;
                        this.validateTableName(table_name);
                        const startTime = Date.now();
                        const query = `DROP TABLE ${if_exists ? 'IF EXISTS' : ''} \`${table_name}\``;
                        const executeResult = await this.executeModification(connection, query);
                        const executionTime = Date.now() - startTime;
                        return this.buildSuccessResponse(executeResult.result, executionTime);
                    }
                    default:
                        throw new Error(`Unknown tool: ${name}`);
                }
            }
            catch (error) {
                // 记录安全事件，用于安全监控
                this.logSecurityEvent('error', { tool: name, error: error instanceof Error ? error.message : String(error) });
                return this.buildErrorResponse(error, { tool: name, operation: 'database_tool_execution' });
            }
            finally {
                // 始终释放连接回连接池，防止连接泄露
                if (connection) {
                    connection.release();
                }
            }
        });
    }
    /**
     * 启动MCP服务器，监听标准输入输出
     */
    async run() {
        const transport = new StdioServerTransport();
        await this.server.connect(transport);
        console.error('MySQL MCP Server running on stdio');
    }
    /**
     * 关闭mcp服务器，清理资源
     */
    async close() {
        if (this.isShuttingDown)
            return;
        this.isShuttingDown = true;
        try {
            // 关闭数据库连接池
            if (this.pool) {
                this.log(LogLevel.INFO, 'Closing database connection pool');
                await this.pool.end();
                this.pool = null;
            }
            // 清理频率限制缓存
            this.rateLimitMap.clear();
            // 记录服务器关闭事件
            this.logSecurityEvent('server_shutdown', { sessionId: this.sessionId });
            this.log(LogLevel.INFO, 'Server shutdown completed');
        }
        catch (error) {
            this.log(LogLevel.ERROR, 'Error during server shutdown', {
                error: error instanceof Error ? error.message : String(error)
            });
            throw error;
        }
    }
}
// MySQL MCP服务器实例，提供MySQL数据库操作功能
const server = new MySQLMCPServer();
// 监听服务器事件
server.on('log', (logEntry) => {
    // 可以在这里添加自定义日志处理逻辑
    if (logEntry.level === LogLevel.SECURITY) {
        // 安全事件特殊处理
        console.error(`[SECURITY] ${logEntry.message}`, logEntry.context);
    }
});
server.on('queryCompleted', (data) => {
    // 查询完成事件处理
    if (data.executionTime > 1000) {
        console.warn(`[PERFORMANCE] Slow query completed in ${data.executionTime}ms`);
    }
});
server.on('queryFailed', (data) => {
    // 查询失败事件处理
    console.error(`[QUERY_FAILED] Query failed after ${data.executionTime}ms: ${data.error}`);
});
// 启动服务器，如果出现错误则输出到控制台
server.run().catch((error) => {
    console.error('Failed to start MySQL MCP server:', error);
    process.exit(1);
});
//# sourceMappingURL=index.js.map